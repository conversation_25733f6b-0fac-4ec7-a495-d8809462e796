// schema/index.ts
import env from "@/env";

// Define the schema type that all database schemas must conform to
export type DatabaseSchema = typeof import("./postgres/schema");

// Dynamic import based on engine
const schemaModule = await (async () => {
  switch (env.DATABASE_ENGINE) {
    case "mysql":
      return import("./mysql/schema");
    case "postgres":
      return import("./postgres/schema");
    case "sqlite":
      return import("./sqlite/schema");
    default:
      throw new Error(
        `Unsupported DATABASE_ENGINE: ${env.DATABASE_ENGINE}. Valid values are 'postgres', 'mysql', or 'sqlite'.`
      );
  }
})();

// Export all schema tables and types
export const {
  users,
  sessions,
  accounts,
  verifications,
  subscriptions,
  payments,
  webhookEvents,
  uploads,
  rateLimits,
  settings,
  userRoleEnum,
} = schemaModule;

// If you need to export the entire schema object
export default schemaModule;