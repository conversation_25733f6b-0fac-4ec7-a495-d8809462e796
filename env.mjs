import { createEnv } from "@t3-oss/env-nextjs";
import { z } from "zod";

const env = createEnv({
  server: {
    DATABASE_ENGINE: z.enum(["postgres", "mysql", "sqlite"]).default("postgres"),

    DATABASE_URL: z.string().url({ message: "Invalid database URL" }),

    // Database creds
    DATABASE_HOST: z.string().default("127.0.0.1"),
    DATABASE_PORT: z.coerce.number().optional(),
    DATABASE_USER: z.string().optional(),
    DATABASE_PASSWORD: z.string().optional(),
    DATABASE_NAME: z.string().optional(),

    // SQLite only
    DATABASE_FILE: z.string().optional(),

    // SSL options
    DATABASE_SSL: z.coerce.boolean().default(false),
    DATABASE_SSL_REJECT_UNAUTHORIZED: z.coerce.boolean().default(false),
    DATABASE_SSL_CA: z.string().optional(),
    DATABASE_SSL_KEY: z.string().optional(),

    SETTINGS_BACKEND: z.enum(["kv", "vercel", "file", "db"]).default("db"),
    SETTINGS_DB_BACKUP: z.coerce.boolean().default(false),

    DB_POOL_SIZE: z.coerce.number().default(20),
    DB_IDLE_TIMEOUT: z.coerce.number().default(300),
    DB_MAX_LIFETIME: z.coerce.number().default(14400),
    DB_CONNECT_TIMEOUT: z.coerce.number().default(30),

    GOOGLE_CLIENT_ID: z.string().optional(),
    GOOGLE_CLIENT_SECRET: z.string().optional(),
    GITHUB_CLIENT_ID: z.string().optional(),
    GITHUB_CLIENT_SECRET: z.string().optional(),
    LINKEDIN_CLIENT_ID: z.string().optional(),
    LINKEDIN_CLIENT_SECRET: z.string().optional(),
    BETTER_AUTH_SECRET: z.string(),

    RESEND_API_KEY: z.string(),

    R2_ENDPOINT: z.string(),
    R2_ACCESS_KEY_ID: z.string(),
    R2_SECRET_ACCESS_KEY: z.string(),
    R2_BUCKET_NAME: z.string(),
    R2_PUBLIC_URL: z.string(),

    CREEM_API_KEY: z.string(),
    CREEM_ENVIRONMENT: z.enum(["test_mode", "live_mode"]).default("test_mode"),
    CREEM_WEBHOOK_SECRET: z.string(),
  },

  client: {
    NEXT_PUBLIC_APP_URL: z.string(),
  },

  runtimeEnv: {
    DATABASE_ENGINE: process.env.DATABASE_ENGINE || "postgres",
    DATABASE_URL: process.env.DATABASE_URL,

    DATABASE_HOST: process.env.DATABASE_HOST,
    DATABASE_PORT: process.env.DATABASE_PORT,
    DATABASE_USER: process.env.DATABASE_USER,
    DATABASE_PASSWORD: process.env.DATABASE_PASSWORD,
    DATABASE_NAME: process.env.DATABASE_NAME,

    DATABASE_FILE: process.env.DATABASE_FILE,

    DATABASE_SSL: process.env.DATABASE_SSL,
    DATABASE_SSL_REJECT_UNAUTHORIZED: process.env.DATABASE_SSL_REJECT_UNAUTHORIZED,
    DATABASE_SSL_CA: process.env.DATABASE_SSL_CA,
    DATABASE_SSL_KEY: process.env.DATABASE_SSL_KEY,

    SETTINGS_BACKEND: process.env.SETTINGS_BACKEND,
    SETTINGS_DB_BACKUP: process.env.SETTINGS_DB_BACKUP,

    DB_POOL_SIZE: process.env.DB_POOL_SIZE,
    DB_IDLE_TIMEOUT: process.env.DB_IDLE_TIMEOUT,
    DB_MAX_LIFETIME: process.env.DB_MAX_LIFETIME,
    DB_CONNECT_TIMEOUT: process.env.DB_CONNECT_TIMEOUT,

    GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
    GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET,
    GITHUB_CLIENT_ID: process.env.GITHUB_CLIENT_ID,
    GITHUB_CLIENT_SECRET: process.env.GITHUB_CLIENT_SECRET,
    LINKEDIN_CLIENT_ID: process.env.LINKEDIN_CLIENT_ID,
    LINKEDIN_CLIENT_SECRET: process.env.LINKEDIN_CLIENT_SECRET,
    BETTER_AUTH_SECRET: process.env.BETTER_AUTH_SECRET,

    RESEND_API_KEY: process.env.RESEND_API_KEY,

    R2_ENDPOINT: process.env.R2_ENDPOINT,
    R2_ACCESS_KEY_ID: process.env.R2_ACCESS_KEY_ID,
    R2_SECRET_ACCESS_KEY: process.env.R2_SECRET_ACCESS_KEY,
    R2_BUCKET_NAME: process.env.R2_BUCKET_NAME,
    R2_PUBLIC_URL: process.env.R2_PUBLIC_URL,

    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
    CREEM_API_KEY: process.env.CREEM_API_KEY,
    CREEM_ENVIRONMENT: process.env.CREEM_ENVIRONMENT,
    CREEM_WEBHOOK_SECRET: process.env.CREEM_WEBHOOK_SECRET,
  },
});

export default env;
