import env from "@/env";
// import * as mysqlCore from "drizzle-orm/mysql-core";
// import * as pgCore from "drizzle-orm/pg-core";
// import * as sqliteCore from "drizzle-orm/sqlite-core";
const engine = env.DATABASE_ENGINE;

let core: any;

async function loadCore() {
  if (engine === "postgres") {
    core = await import("drizzle-orm/pg-core");
  } else if (engine === "mysql") {
    core = await import("drizzle-orm/mysql-core");
  } else if (engine === "sqlite") {
    core = await import("drizzle-orm/sqlite-core");
  } else {
    throw new Error(
      `Unsupported DATABASE_ENGINE: ${engine}. Valid values are 'postgres', 'mysql', or 'sqlite'.`,
    );
  }

  // determine table factory and enum function names across cores
  const tableFn: (...args: any[]) => any = core.pgTable || core.mysqlTable || core.sqliteTable;
  const enumFn: (...args: any[]) => any = core.pgEnum || core.mysqlEnum || core.sqliteEnum || core.sqlEnum;

  if (!tableFn) {
    throw new Error(
      `No table factory found for this database engine. Make sure the installed drizzle core exposes a table factory (pgTable/mysqlTable/sqliteTable).`,
    );
  }

  // Basic type exports with fallbacks
  const types = {
    // Basic types
    boolean: core.boolean || core.int,
    int: core.integer || core.int,
    integer: core.integer || core.int,
    bigint: core.bigint,
    bigInt: core.bigint,
    serial: core.serial || core.int,
    smallint: core.smallint || core.int,
    smallInt: core.smallint || core.int,
    numeric: core.numeric || core.float,
    decimal: core.decimal || core.float,
    float: core.float,
    double: core.double || core.doublePrecision || core.float,
    
    // String & text
    varchar: core.varchar || core.text,
    text: core.text,
    char: core.char || core.varchar,
    
    // JSON
    json: core.json || core.jsonb || core.text,
    jsonb: core.jsonb || core.json || core.text,
    
    // Date/Time
    date: core.date || core.text,
    time: core.time || core.text,
    timestamp: core.timestamp || core.datetime || core.text,
    datetime: core.timestamp || core.datetime || core.text,
    
    // Binary / Blob
    blob: core.blob || core.binary,
    binary: core.binary || core.blob,
    
    // Enum (support multiple naming conventions)
    anyEnum: enumFn,
    pgEnum: enumFn,
    mysqlEnum: enumFn,
    sqliteEnum: enumFn,
    
    // UUID / ID
    uuid: core.uuid || core.text,
    id: core.serial || core.int,
    
    // Other helpers
    primaryKey: core.primaryKey,
    index: core.index,
    uniqueIndex: core.uniqueIndex,
    foreignKey: core.foreignKey,
    
    // Table factories (support multiple naming conventions)
    table: tableFn,
    pgTable: tableFn,
    mysqlTable: tableFn,
    sqliteTable: tableFn,
    makeTable: tableFn,
  };

  return types;
}

const universalSchema = loadCore();

// Export all types and helpers
export const {
  // Basic types
  boolean,
  int,
  integer,
  bigint,
  bigInt,
  serial,
  smallint,
  smallInt,
  numeric,
  decimal,
  float,
  double,
  
  // String & text
  varchar,
  text,
  char,
  
  // JSON
  json,
  jsonb,
  
  // Date/Time
  date,
  time,
  timestamp,
  datetime,
  
  // Binary / Blob
  blob,
  binary,
  
  // Enum
  anyEnum,
  pgEnum,
  mysqlEnum,
  sqliteEnum,
  
  // UUID / ID
  uuid,
  id,
  
  // Other helpers
  primaryKey,
  index,
  uniqueIndex,
  foreignKey,
  
  // Table factories
  table,
  pgTable,
  mysqlTable,
  sqliteTable,
  makeTable,
} = universalSchema;

export default universalSchema;
